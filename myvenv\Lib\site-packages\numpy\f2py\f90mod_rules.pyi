from collections.abc import Mapping
from typing import Any, Final

from .auxfuncs import isintent_dict as isintent_dict

__version__: Final[str] = ...
f2py_version: Final = "See `f2py -v`"

options: Final[dict[str, bool]]

fgetdims1: Final[str] = ...
fgetdims2: Final[str] = ...
fgetdims2_sa: Final[str] = ...

def findf90modules(m: Mapping[str, object]) -> list[dict[str, Any]]: ...
def buildhooks(pymod: Mapping[str, object]) -> dict[str, Any]: ...
